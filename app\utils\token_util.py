import jwt
import time
from config.get_config import config

# 从配置中获取密钥
SECRET_KEY = config.get('jwt', {}).get('secret_key', 'your_secret_key')
ALGORITHM = "HS256"

def verify_token(token: str) -> str:
    """验证JWT令牌并返回用户ID"""
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        user_id = payload.get("user_id")
        exp = payload.get("exp")

        # 检查令牌是否过期
        if exp and time.time() > exp:
            return None

        return user_id
    except:
        return None
