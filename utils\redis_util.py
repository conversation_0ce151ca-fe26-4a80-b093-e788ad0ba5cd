from redis import asyncio as aioredis
# from aioredis import ConnectionsPool
from config.get_config import config

url = (config['db_redis']['host'], config['db_redis']['port'])
host = config['db_redis']['host']
db = config['db_redis']['db']
timeout = config['db_redis']['timeout']
password = config['db_redis']['password']
port = config['db_redis']['port']


# aioredis==2.0.0
# 全局Redis连接实例
_redis_instance = None

async def get_redis():
    """获取Redis连接（作为依赖项使用）"""
    # Redis client bound to pool of connections (auto-reconnecting).
    redis = aioredis.from_url(
        "redis://{}".format(host), db=db, password=password, port=port, encoding="utf-8", decode_responses=True
    )
    yield redis

async def get_redis_instance():
    """获取Redis连接实例（直接调用使用）"""
    global _redis_instance
    if _redis_instance is None:
        _redis_instance = aioredis.from_url(
            "redis://{}".format(host), db=db, password=password, port=port, encoding="utf-8", decode_responses=True
        )
    return _redis_instance
