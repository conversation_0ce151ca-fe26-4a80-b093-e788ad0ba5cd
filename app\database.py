from sqlalchemy import create_engine
from sqlalchemy.orm import declarative_base, sessionmaker
from config.get_config import config
import uuid

# 数据库连接配置
host = config['db_mysql']['host']
port = config['db_mysql']['port']
user = config['db_mysql']['user']
password = config['db_mysql']['psd']
db = config['db_mysql']['database']

SQLALCHEMY_DATABASE_URL = f"mysql+pymysql://{user}:{password}@{host}:{port}/{db}"

engine = create_engine(SQLALCHEMY_DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base()

def get_db():
    """获取数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def generate_id():
    """生成唯一ID"""
    return str(uuid.uuid4()) 