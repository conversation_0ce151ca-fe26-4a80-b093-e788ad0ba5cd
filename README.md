# FastAPI WebSocket 实时聊天系统

## 项目概述

这是一个基于 FastAPI 的高性能 WebSocket 实时聊天系统，专注于提供企业级的实时消息通信解决方案。系统采用微服务架构设计，将 WebSocket 实时通信功能从传统的 PHP 应用中分离出来，实现了高并发、低延迟的消息处理能力。

### 核心特性

- 🚀 **高性能异步架构**: 基于 FastAPI + WebSocket 的异步处理机制
- 💬 **多种消息类型**: 支持文本、图片、文件、卡片等多种消息格式
- 🔄 **实时双向通信**: WebSocket 长连接保证消息实时性
- 📱 **离线消息处理**: Redis 队列管理离线消息和未读计数
- 🛡️ **安全认证机制**: JWT 令牌验证和消息内容过滤
- 💓 **智能心跳检测**: 自动检测连接状态，及时清理无效连接
- 🔍 **内容安全过滤**: 智能识别和过滤手机号码等敏感信息
- 📊 **连接状态监控**: 实时监控 WebSocket 连接状态和统计信息

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│                 │    │                 │    │                 │
│   前端客户端     │◄──►│   FastAPI 服务   │◄──►│   PHP 后端服务   │
│  (WebSocket)    │    │  (WebSocket)    │    │   (HTTP API)    │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │                        │
                              ▼                        ▼
                       ┌─────────────────────────────────────┐
                       │                                     │
                       │        共享数据存储层                │
                       │     MySQL (持久化) + Redis (缓存)    │
                       │                                     │
                       └─────────────────────────────────────┘
```

### 职责分工

#### FastAPI 服务职责
- **WebSocket 连接管理**: 维护客户端长连接，处理连接建立和断开
- **实时消息分发**: 接收并实时推送消息给在线用户
- **心跳检测机制**: 定期检查连接状态，自动清理无效连接
- **Redis 发布订阅**: 处理跨进程消息通信和离线消息队列
- **消息内容过滤**: 智能过滤敏感信息，保障内容安全
- **JWT 令牌验证**: 验证用户身份，确保连接安全性

#### PHP 服务职责
- **用户认证管理**: 用户登录、注册、会话管理
- **聊天室管理**: 创建、管理聊天室和群组
- **消息历史查询**: 提供消息历史记录和搜索功能
- **文件上传处理**: 处理图片、文件等媒体资源上传
- **卡片消息管理**: 管理任务卡片、引用卡片等特殊消息类型
- **前端页面渲染**: 提供聊天界面和管理后台

## 技术栈

### 核心框架
- **FastAPI 0.95.0**: 现代化的高性能 Python Web 框架
- **Uvicorn 0.21.1**: ASGI 服务器，支持异步处理
- **SQLAlchemy 2.0.7**: 强大的 Python ORM 框架
- **PyMySQL 1.0.3**: MySQL 数据库连接驱动

### 通信与缓存
- **WebSocket**: 实时双向通信协议
- **Redis 4.5.4**: 高性能内存数据库，用于消息队列和缓存
- **Redis Pub/Sub**: 发布订阅模式，支持跨进程通信

### 安全与工具
- **PyJWT 2.6.0**: JWT 令牌生成和验证
- **Cryptography 40.0.1**: 加密算法支持
- **Loguru 0.6.0**: 现代化的 Python 日志库
- **Pydantic 1.10.7**: 数据验证和序列化

## 快速开始

### 环境要求
- **Python**: 3.7+ (推荐 3.9+)
- **Redis**: 5.0+ (用于消息队列和缓存)
- **MySQL**: 5.7+ 或 8.0+ (数据持久化)
- **操作系统**: Linux/Windows/macOS

### 安装步骤

#### 1. 克隆项目
```bash
git clone <repository-url>
cd fastapi_ws_chat
```

#### 2. 创建虚拟环境
```bash
python -m venv venv
# Windows
venv\Scripts\activate
# Linux/macOS
source venv/bin/activate
```

#### 3. 安装依赖
```bash
pip install -r requirements.txt
```

#### 4. 配置数据库和服务
编辑 `config/dev_config.toml` 文件：

```toml
[log]
log_path = "/data/log/python/ws/runtime_{time:YYYY-MM}.log"
rotation = "1 month"
format = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} - {message}"
retention = "60 days"

[db_redis]
host = "127.0.0.1"
port = 6379
db = 10
password = "your_redis_password"
timeout = 60000

[db_mysql]
host = "127.0.0.1"
port = 3306
user = "your_mysql_user"
psd = "your_mysql_password"
database = "your_database_name"

[jwt]
secret_key = "your_super_secret_jwt_key_here"
algorithm = "HS256"
access_token_expire_minutes = 1440  # 24小时
```

#### 5. 初始化数据库
系统会自动创建所需的数据表，包括：
- `mf_chatroom`: 聊天室表
- `mf_chat_messages`: 消息表
- `mf_user_chatroom`: 用户聊天室关联表
- `mf_chat_card_confirmations`: 卡片确认记录表
- `mf_task_chatroom`: 任务聊天室表

#### 6. 启动服务

**开发环境**:
```bash
uvicorn main:app --host 0.0.0.0 --port 298 --reload
```

**生产环境**:
```bash
# 使用 Gunicorn + Uvicorn Workers
gunicorn main:app -c config/fastapi_config.py
```

### 验证安装
1. 访问 API 文档: `http://localhost:298/docs`
2. 检查 WebSocket 连接: `ws://localhost:298/api/chat/connect_chat_with_token?token=<your_jwt_token>`
3. 查看连接统计: `http://localhost:298/api/chat/connection/stats`

### 安全配置建议
- 🔐 **JWT 密钥**: 使用强随机密钥，确保 PHP 和 FastAPI 之间一致
- 🌐 **HTTPS/WSS**: 生产环境必须使用加密连接
- 🚫 **CORS 限制**: 限制允许的来源域名
- ⏰ **令牌过期**: 设置合理的令牌过期时间
- 🔒 **Redis 密码**: 为 Redis 设置强密码
- 🛡️ **防火墙**: 限制数据库和 Redis 的网络访问

## API 接口文档

### 自动生成文档
启动服务后，访问以下地址查看完整的 API 文档：
- **Swagger UI**: `http://localhost:298/docs`
- **ReDoc**: `http://localhost:298/redoc`

### 核心 API 端点

#### WebSocket 连接
- **端点**: `/api/chat/connect_chat_with_token`
- **协议**: WebSocket
- **认证**: JWT Token (查询参数)
- **描述**: 建立实时聊天连接

#### HTTP API 端点
| 端点 | 方法 | 描述 | 认证 |
|------|------|------|------|
| `/api/chat/send_message` | POST | 发送消息到聊天室或私聊 | 无 |
| `/api/chat/edit_message` | POST | 编辑已发送的消息 | 无 |
| `/api/chat/create_group` | POST | 创建新的群聊 | 无 |
| `/api/chat/chatrooms/{user_id}` | GET | 获取用户的聊天室列表 | 无 |
| `/api/chat/messages/{chatroom_id}` | GET | 获取聊天室消息历史 | 无 |
| `/api/chat/users/search` | GET | 搜索用户 | 无 |
| `/api/chat/connection/stats` | GET | 获取连接统计信息 | 无 |

## WebSocket 通信协议

### 连接建立
```javascript
const token = "your_jwt_token_here";
const socket = new WebSocket(`ws://localhost:298/api/chat/connect_chat_with_token?token=${token}`);

socket.onopen = function(event) {
    console.log("WebSocket 连接已建立");
};

socket.onmessage = function(event) {
    const data = JSON.parse(event.data);
    console.log("收到消息:", data);
};

socket.onclose = function(event) {
    console.log("WebSocket 连接已关闭");
};
```

### 消息类型规范

#### 1. 基础消息类型

| 消息类型 | 方向 | 描述 | 必需字段 |
|---------|------|------|----------|
| `private_message` | 双向 | 私聊消息 | `type`, `msg`, `sender`, `recipient` |
| `group_message` | 双向 | 群聊消息 | `type`, `msg`, `sender`, `chatroom_id` |
| `image_message` | 双向 | 图片消息 | `type`, `msg`, `sender`, `content_type: "image"` |
| `file_message` | 双向 | 文件消息 | `type`, `msg`, `sender`, `content_type: "file"` |
| `heartbeat` | 双向 | 心跳检测 | `type`, `timestamp` |
| `system` | 服务端→客户端 | 系统通知 | `type`, `msg` |

#### 2. 特殊功能消息

| 消息类型 | 方向 | 描述 | 必需字段 |
|---------|------|------|----------|
| `card_confirmation` | 双向 | 卡片确认 | `type`, `card_id`, `card_type`, `user_id`, `chatroom_id` |
| `card_task` | 双向 | 任务卡片 | `type`, `msg`, `sender`, `content_type: "card"` |
| `card_quote` | 双向 | 引用卡片 | `type`, `msg`, `sender`, `content_type: "card"` |
| `edit_message` | 双向 | 编辑消息 | `type`, `message_id`, `content`, `sender` |
| `create_group` | 客户端→服务端 | 创建群组 | `type`, `name`, `members`, `sender` |

#### 3. 系统响应消息

| 消息类型 | 方向 | 描述 | 字段说明 |
|---------|------|------|----------|
| `new_message` | 服务端→客户端 | 新消息通知 | `type`, `message` (消息对象) |
| `message_sent` | 服务端→客户端 | 消息发送确认 | `type`, `message` (消息对象) |
| `chatroom_list` | 服务端→客户端 | 聊天室列表 | `type`, `chatrooms` (聊天室数组) |
| `unread_counts` | 服务端→客户端 | 未读消息计数 | `type`, `counts` (计数对象) |
| `offline_messages` | 服务端→客户端 | 离线消息 | `type`, `messages` (消息数组) |

### 心跳机制实现

客户端需要定期发送心跳包以维持连接：

```javascript
class ChatWebSocket {
    constructor(token) {
        this.token = token;
        this.heartbeatInterval = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.connect();
    }

    connect() {
        this.socket = new WebSocket(`ws://localhost:298/api/chat/connect_chat_with_token?token=${this.token}`);

        this.socket.onopen = () => {
            console.log("WebSocket 连接成功");
            this.reconnectAttempts = 0;
            this.startHeartbeat();
        };

        this.socket.onmessage = (event) => {
            const data = JSON.parse(event.data);
            this.handleMessage(data);
        };

        this.socket.onclose = () => {
            console.log("WebSocket 连接关闭");
            this.stopHeartbeat();
            this.attemptReconnect();
        };

        this.socket.onerror = (error) => {
            console.error("WebSocket 错误:", error);
        };
    }

    startHeartbeat() {
        this.heartbeatInterval = setInterval(() => {
            if (this.socket.readyState === WebSocket.OPEN) {
                this.socket.send(JSON.stringify({
                    type: "heartbeat",
                    timestamp: Date.now()
                }));
            }
        }, 30000); // 每30秒发送一次心跳
    }

    stopHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
    }

    attemptReconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
            setTimeout(() => this.connect(), 5000);
        }
    }

    sendMessage(message) {
        if (this.socket.readyState === WebSocket.OPEN) {
            this.socket.send(JSON.stringify(message));
        } else {
            console.error("WebSocket 连接未就绪");
        }
    }

    handleMessage(data) {
        switch (data.type) {
            case "new_message":
                this.onNewMessage(data.message);
                break;
            case "chatroom_list":
                this.onChatroomList(data.chatrooms);
                break;
            case "unread_counts":
                this.onUnreadCounts(data.counts);
                break;
            // 处理其他消息类型...
        }
    }
}
```

## 项目结构

```
fastapi_ws_chat/
├── 📁 app/                          # 应用核心模块
│   ├── 📁 api/                      # API 路由模块 (预留)
│   │   └── __init__.py
│   ├── 📁 chat_manager/             # WebSocket 聊天管理
│   │   ├── chat.py                  # WebSocket 路由和 HTTP API
│   │   └── server.py                # 连接管理器和消息处理逻辑
│   ├── 📁 utils/                    # 应用工具类
│   │   ├── message_filter.py        # 消息内容过滤 (手机号等敏感信息)
│   │   ├── oss_util.py              # 阿里云 OSS 文件上传工具
│   │   └── token_util.py            # JWT 令牌验证工具
│   ├── database.py                  # 数据库连接和会话管理
│   ├── dependencies.py             # FastAPI 依赖注入
│   └── models.py                    # SQLAlchemy 数据库模型
├── 📁 config/                       # 配置管理
│   ├── dev_config.toml              # 开发环境配置文件
│   ├── fastapi_config.py            # Gunicorn 生产环境配置
│   └── get_config.py                # 配置加载器
├── 📁 utils/                        # 通用工具类
│   ├── log_util.py                  # 日志配置 (Loguru)
│   ├── redis_queue.py               # Redis 消息队列管理
│   └── redis_util.py                # Redis 连接工具
├── main.py                          # FastAPI 应用入口点
├── requirements.txt                 # Python 依赖包列表
└── README.md                        # 项目文档
```

### 核心模块说明

#### 🔌 WebSocket 管理 (`app/chat_manager/`)
- **`server.py`**: 核心连接管理器，处理 WebSocket 连接生命周期、消息路由、心跳检测
- **`chat.py`**: WebSocket 路由定义和 HTTP API 端点

#### 🗄️ 数据层 (`app/models.py`, `app/database.py`)
- **数据库模型**: 聊天室、消息、用户关联、卡片确认等
- **连接管理**: SQLAlchemy 引擎和会话管理

#### 🛠️ 工具类 (`app/utils/`, `utils/`)
- **消息过滤**: 智能识别和过滤敏感信息
- **Redis 队列**: 离线消息和未读计数管理
- **文件上传**: 阿里云 OSS 集成
- **JWT 验证**: 用户身份认证

#### ⚙️ 配置管理 (`config/`)
- **环境配置**: 数据库、Redis、JWT 等配置
- **部署配置**: Gunicorn 生产环境配置

## 数据库设计

### 核心数据表

#### 1. 聊天室表 (`mf_chatroom`)
```sql
CREATE TABLE mf_chatroom (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100),              -- 群聊名称 (私聊为空)
    is_group BOOLEAN DEFAULT FALSE, -- 是否为群聊
    created_at DATETIME DEFAULT NOW()
);
```

#### 2. 消息表 (`mf_chat_messages`)
```sql
CREATE TABLE mf_chat_messages (
    id INT PRIMARY KEY AUTO_INCREMENT,
    content TEXT NOT NULL,          -- 消息内容
    sender_id VARCHAR(36),          -- 发送者ID
    chatroom_id INT,                -- 聊天室ID
    message_type VARCHAR(50),       -- 消息类型 (text/image/file/card)
    content_type VARCHAR(50) DEFAULT 'text', -- 内容类型
    is_html BOOLEAN DEFAULT FALSE,  -- 是否包含HTML
    is_edited BOOLEAN DEFAULT FALSE, -- 是否已编辑
    created_at DATETIME DEFAULT NOW(),
    updated_at DATETIME DEFAULT NOW() ON UPDATE NOW()
);
```

#### 3. 用户聊天室关联表 (`mf_user_chatroom`)
```sql
CREATE TABLE mf_user_chatroom (
    user_id VARCHAR(36),            -- 用户ID
    chatroom_id INT,                -- 聊天室ID
    user_type VARCHAR(36),          -- 用户角色
    user_type_name VARCHAR(36),     -- 角色名称
    user_name VARCHAR(36),          -- 用户名
    PRIMARY KEY (user_id, chatroom_id)
);
```

#### 4. 卡片确认记录表 (`mf_chat_card_confirmations`)
```sql
CREATE TABLE mf_chat_card_confirmations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    card_id VARCHAR(100) NOT NULL,  -- 卡片ID
    card_type VARCHAR(50) NOT NULL, -- 卡片类型
    message_id INT,                 -- 关联消息ID
    chatroom_id INT NOT NULL,       -- 聊天室ID
    confirmed_by VARCHAR(36) NOT NULL, -- 确认用户ID
    confirmed_at DATETIME DEFAULT NOW()
);
```

## 系统通信流程

### 1. 连接建立流程
```mermaid
sequenceDiagram
    participant C as 客户端
    participant F as FastAPI
    participant R as Redis
    participant M as MySQL

    C->>F: WebSocket 连接请求 (JWT Token)
    F->>F: 验证 JWT Token
    F->>M: 查询用户信息
    F->>C: 连接建立成功
    F->>C: 发送聊天室列表
    F->>R: 获取未读消息计数
    F->>C: 发送未读计数
    F->>R: 获取离线消息
    F->>C: 推送离线消息
```

### 2. 消息发送流程
```mermaid
sequenceDiagram
    participant C1 as 发送者
    participant F as FastAPI
    participant R as Redis
    participant M as MySQL
    participant C2 as 接收者

    C1->>F: 发送消息 (WebSocket)
    F->>F: 消息内容过滤
    F->>M: 保存消息到数据库

    alt 接收者在线
        F->>C2: 实时推送消息
    else 接收者离线
        F->>R: 添加到离线消息队列
        F->>R: 更新未读计数
    end

    F->>C1: 发送确认回执
```

### 3. 心跳检测机制
- **客户端**: 每 30 秒发送心跳包
- **服务端**: 记录最后活动时间，每 30 秒检查超时连接
- **超时处理**: 120 秒无响应自动断开连接并清理资源

### 4. Redis 发布订阅
- **频道**: `chat` 频道用于跨进程消息通信
- **消息格式**: JSON 格式，包含消息类型和相关数据
- **处理逻辑**: 异步监听并分发给对应的 WebSocket 连接

## 安全特性

### 1. 身份认证
- **JWT 令牌**: 基于 HS256 算法的令牌验证
- **令牌过期**: 可配置的过期时间 (默认 24 小时)
- **连接验证**: WebSocket 连接时验证令牌有效性

### 2. 内容安全
- **手机号过滤**: 智能识别各种格式的手机号码
- **分段检测**: 检测分段发送的手机号码
- **HTML 过滤**: 支持 HTML 内容的安全处理

### 3. 连接安全
- **心跳检测**: 防止僵尸连接占用资源
- **自动重连**: 客户端自动重连机制
- **连接限制**: 可配置的连接数限制

## 性能优化

### 1. 异步处理
- **FastAPI**: 基于 asyncio 的异步框架
- **WebSocket**: 异步 WebSocket 处理
- **数据库**: 异步数据库操作

### 2. 缓存策略
- **Redis 缓存**: 离线消息和未读计数缓存
- **连接池**: 数据库和 Redis 连接池
- **消息队列**: 高效的消息队列处理

### 3. 资源管理
- **连接清理**: 自动清理无效连接
- **内存优化**: 合理的数据结构和内存使用
- **日志管理**: 结构化日志和日志轮转

## 部署指南

### 开发环境部署

#### 1. 本地开发
```bash
# 启动开发服务器
uvicorn main:app --host 0.0.0.0 --port 298 --reload

# 或使用 Python 直接运行
python main.py
```

#### 2. Docker 部署 (可选)
```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 298

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "298"]
```

### 生产环境部署

#### 1. 使用 Gunicorn + Uvicorn Workers
```bash
# 安装 Gunicorn
pip install gunicorn

# 启动生产服务器
gunicorn main:app -c config/fastapi_config.py
```

#### 2. 使用 Supervisor 进程管理
```ini
[program:fastapi_ws_chat]
command=/path/to/venv/bin/gunicorn main:app -c config/fastapi_config.py
directory=/path/to/fastapi_ws_chat
user=www-data
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/fastapi_ws_chat.log
```

#### 3. Nginx 反向代理配置
```nginx
upstream fastapi_backend {
    server 127.0.0.1:298;
}

server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://fastapi_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # WebSocket 支持
    location /api/chat/connect_chat_with_token {
        proxy_pass http://fastapi_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 86400;
    }
}
```

## 监控与运维

### 1. 日志管理
```python
# 日志配置 (config/dev_config.toml)
[log]
log_path = "/var/log/fastapi_ws_chat/runtime_{time:YYYY-MM}.log"
rotation = "1 month"
format = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} - {message}"
retention = "60 days"
```

### 2. 健康检查
```bash
# 检查服务状态
curl http://localhost:298/api/chat/connection/stats

# 检查 WebSocket 连接
wscat -c ws://localhost:298/api/chat/connect_chat_with_token?token=your_token
```

### 3. 性能监控
- **连接数监控**: 通过 `/api/chat/connection/stats` 端点
- **Redis 监控**: 监控 Redis 内存使用和连接数
- **数据库监控**: 监控 MySQL 连接池和查询性能

## 故障排除

### 常见问题

#### 1. WebSocket 连接失败
```bash
# 检查令牌是否有效
# 检查 JWT 密钥配置
# 检查网络连接和防火墙设置
```

#### 2. 消息发送失败
```bash
# 检查 Redis 连接状态
# 检查数据库连接
# 查看应用日志
```

#### 3. 心跳超时
```bash
# 检查客户端心跳实现
# 调整心跳间隔和超时时间
# 检查网络稳定性
```

### 调试技巧

#### 1. 启用详细日志
```python
# 在 main.py 中添加
import logging
logging.basicConfig(level=logging.DEBUG)
```

#### 2. 使用 WebSocket 测试工具
```bash
# 安装 wscat
npm install -g wscat

# 测试连接
wscat -c "ws://localhost:298/api/chat/connect_chat_with_token?token=your_token"
```

## 扩展开发

### 1. 添加新的消息类型
```python
# 在 server.py 的 handle_message 方法中添加新的消息类型处理
elif msg_type == "your_new_message_type":
    # 处理逻辑
    pass
```

### 2. 自定义消息过滤器
```python
# 在 message_filter.py 中扩展 MessageFilter 类
class MessageFilter:
    @staticmethod
    def filter_custom_content(content: str) -> Tuple[bool, str, str]:
        # 自定义过滤逻辑
        pass
```

### 3. 集成第三方服务
```python
# 在 utils/ 目录下添加新的工具类
# 例如: sms_util.py, email_util.py 等
```

## 贡献指南

### 开发规范
- 遵循 PEP 8 Python 代码规范
- 使用类型注解提高代码可读性
- 编写单元测试覆盖核心功能
- 更新文档说明新增功能

### 提交流程
1. Fork 项目仓库
2. 创建功能分支
3. 编写代码和测试
4. 提交 Pull Request
5. 代码审查和合并

## 许可证

本项目采用 MIT 许可证，详情请参阅 LICENSE 文件。

## 联系方式

- **项目维护者**: [维护者姓名]
- **邮箱**: [联系邮箱]
- **问题反馈**: [GitHub Issues 链接]

---

**最后更新**: 2024年12月

**版本**: v1.0.0
