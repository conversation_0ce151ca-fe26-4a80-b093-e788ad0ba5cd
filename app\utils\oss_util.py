# -*- coding: utf-8 -*-
import oss2
import uuid
import os
from datetime import datetime

class OssUtil:
    """阿里云OSS工具类"""

    def __init__(self):
        # 阿里云主账号AccessKey
        self.access_key_id = 'LTAI5tC67oQ86AXcCH4SAe5G'
        self.access_key_secret = '******************************'

        # OSS配置
        self.endpoint = "https://oss-cn-hangzhou.aliyuncs.com"
        self.region = "cn-hangzhou"
        self.bucket_name = "mfstatic"

        # 创建认证对象
        self.auth = oss2.Auth(self.access_key_id, self.access_key_secret)

        # 创建Bucket实例
        self.bucket = oss2.Bucket(self.auth, self.endpoint, self.bucket_name, region=self.region)

        # 图片访问的基础URL
        self.base_url = "https://static.mfcad.com"

    def upload_image(self, image_data, image_ext='.jpg'):
        """
        上传图片到OSS

        Args:
            image_data: 图片数据（二进制）
            image_ext: 图片扩展名，默认为.jpg

        Returns:
            str: 图片URL
        """
        try:
            # 生成唯一的文件名
            today = datetime.now().strftime('%Y%m%d')
            filename = f"{uuid.uuid4().hex}{image_ext}"

            # 构建对象路径，按日期分类存储
            object_name = f"websocket/{today}/{filename}"

            # 计算数据大小
            data_size_mb = len(image_data) / 1024 / 1024
            print(f"正在上传图片到OSS，大小: {data_size_mb:.2f}MB")

            # 上传图片数据
            self.bucket.put_object(object_name, image_data)

            print(f"图片上传成功，对象名: {object_name}")

            # 返回可访问的URL
            return f"{self.base_url}/{object_name}"
        except Exception as e:
            print(f"上传图片到OSS失败: {str(e)}")
            return None

    def upload_image_from_file(self, local_file_path):
        """
        从本地文件上传图片到OSS

        Args:
            local_file_path: 本地文件路径

        Returns:
            str: 图片URL
        """
        try:
            # 获取文件扩展名
            _, ext = os.path.splitext(local_file_path)

            # 生成唯一的文件名
            today = datetime.now().strftime('%Y%m%d')
            filename = f"{uuid.uuid4().hex}{ext}"

            # 构建对象路径，按日期分类存储
            object_name = f"websocket/{today}/{filename}"

            # 上传本地文件
            self.bucket.put_object_from_file(object_name, local_file_path)

            # 返回可访问的URL
            return f"{self.base_url}/{object_name}"
        except Exception as e:
            print(f"从本地文件上传图片到OSS失败: {str(e)}")
            return None

    def delete_image(self, image_url):
        """
        删除OSS上的图片

        Args:
            image_url: 图片URL

        Returns:
            bool: 是否删除成功
        """
        try:
            # 从URL中提取对象名称
            object_name = image_url.replace(self.base_url + "/", "")

            # 删除对象
            self.bucket.delete_object(object_name)
            return True
        except Exception as e:
            print(f"删除OSS图片失败: {str(e)}")
            return False

# 创建工具类实例
oss_util = OssUtil()
