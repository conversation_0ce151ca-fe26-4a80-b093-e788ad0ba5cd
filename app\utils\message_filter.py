import re
from typing import Tu<PERSON>, List, Dict

class MessageFilter:
    """消息内容过滤工具类"""

    # 存储最近的消息历史，用于检测分段发送的手机号码
    # 格式: {user_id: [最近的消息列表]}
    _message_history: Dict[str, List[str]] = {}

    @staticmethod
    def filter_phone_number(content: str, user_id: str = None) -> Tuple[bool, str, str]:
        """
        过滤消息中的手机号码

        Args:
            content: 消息内容
            user_id: 用户ID，用于跟踪用户消息历史

        Returns:
            Tuple[bool, str, str]: (是否包含手机号码, 过滤后的内容, 错误信息)
        """
        # 处理空内容
        if not content or not content.strip():
            return False, content, ""

        # 1. 检测常规手机号码格式
        # 中国大陆手机号码正则表达式 - 标准格式
        standard_pattern = r'1[3-9]\d{9}'

        # 2. 检测带空格、横杠、点等分隔符的手机号码
        # 例如: 199 3315 2686, 199-3315-2686, 199.3315.2686
        spaced_pattern = r'1[3-9][\s\-\.]{0,2}\d{1,5}[\s\-\.]{0,2}\d{1,5}[\s\-\.]{0,2}\d{1,5}'

        # 3. 移除所有空格、横杠等字符后再检测
        cleaned_content = re.sub(r'[\s\-\.]+', '', content)

        # 检查是否包含手机号码 - 标准格式
        contains_phone = False
        filtered_content = content

        # 检查标准格式
        if re.search(standard_pattern, content):
            filtered_content = re.sub(standard_pattern, '********', filtered_content)
            contains_phone = True

        # 检查带分隔符的格式
        if re.search(spaced_pattern, filtered_content):
            filtered_content = re.sub(spaced_pattern, '********', filtered_content)
            contains_phone = True

        # 检查清理后的内容是否包含手机号码
        if not contains_phone and re.search(standard_pattern, cleaned_content):
            # 如果清理后的内容包含手机号码，但原内容没有被过滤，说明是特殊格式
            # 这种情况下，我们需要更复杂的替换逻辑
            phone_matches = re.finditer(standard_pattern, cleaned_content)
            for match in phone_matches:
                phone = match.group(0)
                # 构建一个正则表达式，匹配原始内容中对应的部分
                # 这个正则表达式会匹配数字和可能的分隔符
                pattern_parts = []
                for digit in phone:
                    pattern_parts.append(f"{digit}[\\s\\-\\.]*")
                complex_pattern = ''.join(pattern_parts).rstrip('[\\s\\-\\.]*')
                filtered_content = re.sub(complex_pattern, '********', filtered_content)
                contains_phone = True

        # 4. 处理分段发送的手机号码
        if user_id and not contains_phone:
            # 获取用户最近的消息历史
            user_history = MessageFilter._message_history.get(user_id, [])

            # 添加当前消息到历史
            user_history.append(content)
            # 只保留最近的5条消息
            user_history = user_history[-5:]
            MessageFilter._message_history[user_id] = user_history

            # 合并最近的消息，检查是否包含手机号码
            combined_content = ''.join(user_history)
            cleaned_combined = re.sub(r'[\s\-\.]+', '', combined_content)

            if re.search(standard_pattern, cleaned_combined):
                # 如果合并后的内容包含手机号码，过滤当前消息
                # 这里我们采用保守策略，如果检测到分段发送的手机号码，就完全替换当前消息
                if content.strip() and len(content.strip()) <= 6:  # 只处理短消息，避免误判
                    filtered_content = '********'
                    contains_phone = True

        if contains_phone:
            return True, filtered_content, "消息中包含手机号码，已被过滤"

        return False, content, ""
